<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Preview - <?php echo e($record->nomor_invoice); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .logo-section {
            flex: 1;
        }

        .company-info {
            flex: 2;
            text-align: center;
            padding: 0 20px;
        }

        .invoice-info {
            flex: 1;
            text-align: right;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .company-tagline {
            font-size: 11px;
            color: #7f8c8d;
            margin-bottom: 2px;
        }

        .company-contact {
            font-size: 10px;
            color: #95a5a6;
            margin-top: 10px;
        }

        .invoice-box {
            border: 2px solid #3498db;
            padding: 15px;
            border-radius: 5px;
            background-color: #ecf0f1;
        }

        .invoice-label {
            font-size: 10px;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .document-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 25px;
        }

        .detail-section h4 {
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
        }

        .detail-table {
            width: 100%;
        }

        .detail-table td {
            padding: 5px 0;
            font-size: 11px;
        }

        .detail-table td:first-child {
            font-weight: bold;
            color: #2c3e50;
            width: 40%;
        }

        .detail-table td:nth-child(2) {
            width: 5%;
            text-align: center;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 11px;
        }

        .items-table th {
            background-color: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
        }

        .items-table td {
            border: 1px solid #bdc3c7;
            padding: 10px 8px;
            text-align: center;
            vertical-align: middle;
        }

        .items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .items-table tbody tr:hover {
            background-color: #e8f4f8;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 25px;
        }

        .totals-table {
            width: 300px;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            font-size: 11px;
        }

        .totals-table td:first-child {
            font-weight: bold;
            background-color: #f8f9fa;
            text-align: right;
        }

        .totals-table td:last-child {
            text-align: right;
        }

        .total-final {
            background-color: #34495e !important;
            color: white !important;
            font-weight: bold !important;
        }

        .notes-section {
            margin-top: 25px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .notes-content {
            border: 1px solid #bdc3c7;
            padding: 15px;
            min-height: 60px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #7f8c8d;
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }

        .badge-success {
            background-color: #27ae60;
            color: white;
        }

        .badge-warning {
            background-color: #f39c12;
            color: white;
        }

        .badge-info {
            background-color: #3498db;
            color: white;
        }

        .badge-danger {
            background-color: #e74c3c;
            color: white;
        }

        @media print {
            body {
                background-color: white;
                padding: 0;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo-section">
                <div
                    style="width: 100px; height: 80px; border: 2px dashed #bdc3c7; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #7f8c8d;">
                    COMPANY<br>LOGO
                </div>
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                <div class="company-tagline">Fuel Agent - Fuel Transportation - Bunker Service</div>
                <div class="company-contact">
                    <?php if($record->letterSetting): ?>
                        <?php echo e($record->letterSetting->phone_number ?? '0761-22369'); ?> -
                        <?php echo e($record->letterSetting->email ?? '<EMAIL>'); ?><br>
                        <?php echo e($record->letterSetting->website ?? 'www.lintasriauprima.com'); ?>

                    <?php else: ?>
                        0761-22369 - <EMAIL><br>
                        www.lintasriauprima.com
                    <?php endif; ?>
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-box">
                    <div class="invoice-label">Invoice No:</div>
                    <div style="font-weight: bold; font-size: 12px; color: #2c3e50;">
                        <?php echo e($record->nomor_invoice ?? 'N/A'); ?>

                    </div>
                    <div style="margin-top: 10px;">
                        <div class="invoice-label">Status:</div>
                        <span
                            class="badge <?php echo e($record->status === 'paid' ? 'badge-success' : ($record->status === 'draft' ? 'badge-warning' : 'badge-info')); ?>">
                            <?php echo e(ucfirst($record->status ?? 'draft')); ?>

                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Title -->
        <div class="document-title">SALES INVOICE</div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="detail-section">
                <h4>Customer Information</h4>
                <table class="detail-table">
                    <tr>
                        <td>Customer</td>
                        <td>:</td>
                        <td><?php echo e($record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A')); ?>

                        </td>
                    </tr>
                    <tr>
                        <td>Address</td>
                        <td>:</td>
                        <td><?php echo e($record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamat ?? 'N/A')); ?>

                        </td>
                    </tr>
                    <tr>
                        <td>Tax ID</td>
                        <td>:</td>
                        <td><?php echo e($record->npwp_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->npwp ?? '-')); ?>

                        </td>
                    </tr>
                </table>
            </div>
            <div class="detail-section">
                <h4>Invoice Information</h4>
                <table class="detail-table">
                    <tr>
                        <td>Invoice Date</td>
                        <td>:</td>
                        <td><?php echo e($record->tanggal_invoice ? $record->tanggal_invoice->format('d M Y') : 'N/A'); ?></td>
                    </tr>
                    <tr>
                        <td>Due Date</td>
                        <td>:</td>
                        <td><?php echo e($record->tanggal_jatuh_tempo ? $record->tanggal_jatuh_tempo->format('d M Y') : 'N/A'); ?>

                        </td>
                    </tr>
                    <tr>
                        <td>Transaction</td>
                        <td>:</td>
                        <td><?php echo e($record->transaksiPenjualan?->kode ?? 'N/A'); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">No</th>
                    <th style="width: 45%;">Description</th>
                    <th style="width: 15%;">Quantity</th>
                    <th style="width: 15%;">Unit Price</th>
                    <th style="width: 20%;">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $itemNumber = 1;
                    $subtotalCalculated = 0;
                ?>

                <?php if($record->invoiceItems && $record->invoiceItems->count() > 0): ?>
                    <?php $__currentLoopData = $record->invoiceItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $total = $item->quantity * $item->unit_price;
                            $subtotalCalculated += $total;
                        ?>
                        <tr>
                            <td><?php echo e($itemNumber++); ?></td>
                            <td style="text-align: left; padding-left: 10px;">
                                <?php echo e($item->description ?? ($item->item?->name ?? 'N/A')); ?>

                            </td>
                            <td><?php echo e(number_format($item->quantity ?? 0, 0, ',', '.')); ?> <?php echo e($item->unit ?? 'L'); ?></td>
                            <td><?php echo e($record->formatCurrency($item->unit_price ?? 0)); ?></td>
                            <td><?php echo e($record->formatCurrency($total)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php if($record->transaksiPenjualan && $record->transaksiPenjualan->penjualanDetails): ?>
                        <?php $__currentLoopData = $record->transaksiPenjualan->penjualanDetails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $total = $detail->volume_item * $detail->harga_jual;
                                $subtotalCalculated += $total;
                            ?>
                            <tr>
                                <td><?php echo e($itemNumber++); ?></td>
                                <td style="text-align: left; padding-left: 10px;">
                                    <?php echo e($detail->item->name ?? 'N/A'); ?>

                                </td>
                                <td><?php echo e(number_format($detail->volume_item ?? 0, 0, ',', '.')); ?> L</td>
                                <td><?php echo e($record->formatCurrency($detail->harga_jual ?? 0)); ?></td>
                                <td><?php echo e($record->formatCurrency($total)); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" style="text-align: center; color: #7f8c8d; padding: 20px;">
                                No items in this invoice
                            </td>
                        </tr>
                    <?php endif; ?>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td>Subtotal</td>
                    <td><?php echo e($record->formatCurrency($record->subtotal ?? $subtotalCalculated)); ?></td>
                </tr>
                <?php if($record->include_ppn): ?>
                    <tr>
                        <td>VAT 11%</td>
                        <td><?php echo e($record->formatCurrency($record->total_pajak ?? 0)); ?></td>
                    </tr>
                <?php endif; ?>
                <?php if($record->include_operasional_kerja && $record->biaya_operasional_kerja > 0): ?>
                    <tr>
                        <td>Operational Fee</td>
                        <td><?php echo e($record->formatCurrency($record->biaya_operasional_kerja ?? 0)); ?></td>
                    </tr>
                <?php endif; ?>
                <?php if($record->include_pbbkb && $record->biaya_pbbkb > 0): ?>
                    <tr>
                        <td>PBBKB Fee</td>
                        <td><?php echo e($record->formatCurrency($record->biaya_pbbkb ?? 0)); ?></td>
                    </tr>
                <?php endif; ?>
                <tr class="total-final">
                    <td>Total Invoice</td>
                    <td><?php echo e($record->formatCurrency($record->total_invoice ?? ($record->calculated_total ?? 0))); ?></td>
                </tr>
            </table>
        </div>

        <!-- Notes Section -->
        <div class="notes-section">
            <div class="notes-title">Notes:</div>
            <div class="notes-content">
                <?php echo e($record->catatan ?? 'No additional notes.'); ?>

            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <strong>PT. LINTAS RIAU PRIMA</strong><br>
            <?php if($record->letterSetting): ?>
                <?php echo e($record->letterSetting->address); ?><br>
                Phone: <?php echo e($record->letterSetting->phone_number); ?> | Email: <?php echo e($record->letterSetting->email); ?> |
                Website: <?php echo e($record->letterSetting->website); ?>

            <?php else: ?>
                Jl. Raya Lintas Timur KM 16, Kelurahan Tuah Karya, Kecamatan Tampan, Kota Pekanbaru, Riau 28293<br>
                Phone: 0761-22369 | Email: <EMAIL> | Website: www.lintasriauprima.com
            <?php endif; ?>
        </div>
    </div>
</body>

</html>
<?php /**PATH D:\laragon\www\lrp\resources\views\invoice\invoice-preview-en.blade.php ENDPATH**/ ?>