<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Delivery Order - <?php echo e($record->kode); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 20px;
            background: white;
        }

        .header {
            width: 100%;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 25%;
            vertical-align: top;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: center;
            padding: 0 10px;
        }

        .recipient-info {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            text-align: right;
        }

        .logo {
            width: 80px;
            height: auto;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .company-address {
            font-size: 10px;
            line-height: 1.2;
        }

        .document-title {
            font-size: 18px;
            font-weight: bold;
            text-decoration: underline;
            margin: 20px 0;
            text-align: center;
        }

        .info-section {
            margin-bottom: 15px;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .info-table td {
            padding: 3px 5px;
            vertical-align: top;
        }

        .info-label {
            width: 120px;
            font-weight: bold;
        }

        .info-colon {
            width: 10px;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 8px 5px;
            text-align: center;
            vertical-align: middle;
        }

        .main-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 10px;
        }

        .main-table td {
            font-size: 10px;
        }

        .text-left {
            text-align: left !important;
        }

        .text-right {
            text-align: right !important;
        }

        .notes-section {
            margin: 20px 0;
        }

        .notes-table {
            width: 100%;
            border-collapse: collapse;
        }

        .notes-table td {
            border: 1px solid #000;
            padding: 8px;
            vertical-align: top;
        }

        .signature-container {
            display: table;
            width: 100%;
            margin-top: 30px;
        }

        .signature-section {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .signature-title {
            font-size: 10px;
            margin-bottom: 5px;
        }

        .signature-space {
            height: 60px;
            border-bottom: 1px solid #000;
            margin: 10px 0;
        }

        .signature-name {
            font-size: 10px;
            font-weight: bold;
            margin: 5px 0;
        }

        .signature-role {
            font-size: 9px;
            line-height: 1.2;
        }

        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }

        .page-break {
            page-break-after: always;
        }

        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <?php if(isset($logoBase64) && $logoBase64): ?>
                    <img src="data:image/png;base64,<?php echo e($logoBase64); ?>" alt="Company Logo" class="logo">
                <?php else: ?>
                    <div
                        style="width: 80px; height: 60px; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 8px;">
                        LOGO
                    </div>
                <?php endif; ?>
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div style="font-size: 10px; font-weight: bold; margin-bottom: 2px;">TRUSTED & RELIABLE PARTNER</div>
                <div style="font-size: 9px; margin-bottom: 3px;">Fuel Agent - Fuel Transportation - Bunker Service</div>
                <div style="font-size: 9px;">
                    0761-22369 - <EMAIL><br>
                    www.lintasriauprima.com
                </div>
            </div>
            
            <div class="recipient-info">
                <div style="border: 1px solid #000; padding: 8px; font-size: 10px;">
                    <div style="margin-bottom: 5px;">Kepada Yth.</div>
                    <div style="font-weight: bold; font-size: 11px;">
                        <?php echo e(strtoupper($record->transaksi->pelanggan->nama ?? 'N/A')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Title -->
    <div class="document-title">BUKTI PENGIRIMAN BARANG / DELIVERY ORDER (DO)</div>

    <!-- DO Information -->
    <div style="display: table; width: 100%; margin-bottom: 20px; font-size: 12px;">
        <div style="display: table-cell; width: 50%; text-align: left;">
            <strong>Nomor DO : <?php echo e($record->kode ?? 'N/A'); ?></strong>
        </div>
        <div style="display: table-cell; width: 50%; text-align: right;">
            <strong>LRP-Form-Ops-04/Rev 03/
                <?php echo e($record->transaksi && $record->transaksi->created_at ? $record->transaksi->created_at->format('d M Y') : now()->format('d M Y')); ?></strong>
        </div>
    </div>

    <!-- Items Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 40%;">JENIS BARANG</th>
                <th style="width: 15%;">JUMLAH</th>
                <th style="width: 25%;">NO SEGEL</th>
                <th style="width: 15%;">PENERIMA/PIC</th>
                <th style="width: 15%;">TANDA TANGAN</th>
            </tr>
        </thead>
        <tbody>
            <?php
                $totalVolume = 0;
                $itemNumber = 1;
            ?>

            <?php if($record->transaksi && $record->transaksi->penjualanDetails && $record->transaksi->penjualanDetails->count() > 0): ?>
                <?php $__currentLoopData = $record->transaksi->penjualanDetails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $totalVolume += $detail->volume_do;
                    ?>
                    <tr>
                        <td><?php echo e($itemNumber++); ?>.</td>
                        <td class="text-left">
                            <?php echo e($detail->item->name ?? 'BBM INDUSTRI'); ?>

                        </td>
                        <td><?php echo e(number_format($detail->volume_do, 0)); ?> <?php echo e($detail->item->satuan->nama ?? 'ltr'); ?>

                        </td>
                        <td>
                            <?php if($record->seals && $record->seals->count() > 0): ?>
                                <?php echo e($record->seals->pluck('nomor_segel')->implode(', ')); ?>

                            <?php elseif($record->no_segel): ?>
                                <?php echo e($record->no_segel); ?>

                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <tr>
                    <td>1.</td>
                    <td class="text-left">No items found</td>
                    <td>0 ltr</td>
                    <td>
                        <?php if($record->seals && $record->seals->count() > 0): ?>
                            <?php echo e($record->seals->pluck('nomor_segel')->implode(', ')); ?>

                        <?php elseif($record->no_segel): ?>
                            <?php echo e($record->no_segel); ?>

                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($record->transaksi->pelanggan->pic_nama ?? '-'); ?></td>
                    <td></td>
                </tr>
            <?php endif; ?>

            <!-- Total Row -->
            <tr class="total-row" style="font-weight: bold; background-color: #f9f9f9;">
                <td colspan="2" style="text-align: center;"><strong>TOTAL</strong></td>
                <td><strong><?php echo e(number_format($totalVolume, 0)); ?> ltr</strong></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </tbody>
    </table>

    <!-- Notes Section -->
    <div style="margin: 20px 0; font-size: 10px;">
        <div style="font-weight: bold; margin-bottom: 5px;">Catatan :</div>
        <div style="line-height: 1.4;">
            - Periksa kualitas dan kuantitas barang sebelum bongkar, setelah bingkar keluhan tidak dilayani<br>
            - Periksa seluruh segel atas dan bawah harus dalam kondisi baik sebelum dibongkar
        </div>
    </div>

    <!-- Signatures Section -->
    <div style="display: table; width: 100%; margin-top: 30px; font-size: 10px;">
        <div style="display: table-cell; width: 25%; text-align: center; vertical-align: top; padding: 0 5px;">
            <div style="margin-bottom: 5px;">.............../............................. <?php echo e(now()->format('d')); ?>...
            </div>
            <div style="margin-bottom: 5px;">Penerima,</div>
            <div style="height: 60px; border-bottom: 1px solid #000; margin: 10px 0;"></div>
            <div style="font-weight: bold; margin: 5px 0;">(...........................)</div>
            <div style="font-size: 9px; line-height: 1.2;">Nama & Tanda Tangan<br>atau Cap Perusahaan</div>
        </div>

        <div style="display: table-cell; width: 25%; text-align: center; vertical-align: top; padding: 0 5px;">
            <div style="margin-bottom: 5px;">Security (jika ada),</div>
            <div style="height: 60px; border-bottom: 1px solid #000; margin: 10px 0;"></div>
            <div style="font-weight: bold; margin: 5px 0;">(...........................)</div>
            <div style="font-size: 9px; line-height: 1.2;">Nama & Tanda Tangan<br>atau Cap Perusahaan</div>
        </div>

        <div style="display: table-cell; width: 25%; text-align: center; vertical-align: top; padding: 0 5px;">
            <div style="margin-bottom: 5px;">Pekanbaru, /........................... <?php echo e(now()->format('d')); ?>...</div>
            <div style="margin-bottom: 5px;">Pengantar,</div>
            <div style="height: 60px; border-bottom: 1px solid #000; margin: 10px 0;"></div>
            <div style="font-weight: bold; margin: 5px 0;">( Zaiful Amri )</div>
            <div style="font-size: 9px; line-height: 1.2;">BM 8524 JO<br>Nama & Tanda Tangan</div>
        </div>

        <div style="display: table-cell; width: 25%; text-align: center; vertical-align: top; padding: 0 5px;">
            <div style="margin-bottom: 5px;">Pengirim,</div>
            <div style="height: 60px; border-bottom: 1px solid #000; margin: 10px 0;"></div>
            <div style="font-weight: bold; margin: 5px 0;">(...........................)</div>
            <div style="font-size: 9px; line-height: 1.2;">Nama & Tanda Tangan<br>atau Cap Perusahaan</div>
        </div>
    </div>

    <!-- Additional Information -->
    <div style="margin-top: 30px; font-size: 10px;">
        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 50%;">
                <strong>Informasi Kendaraan:</strong><br>
                Plat Nomor: <?php echo e($record->kendaraan->no_pol_kendaraan ?? 'N/A'); ?><br>
                Driver: <?php echo e($record->user->name ?? 'N/A'); ?>

            </div>
            <div style="display: table-cell; width: 50%; text-align: right;">
                <strong>Pekanbaru, <?php echo e(now()->format('d/m/Y')); ?></strong><br>
                Tanggal Pengiriman:
                <?php echo e($record->tanggal_delivery ? $record->tanggal_delivery->format('d/m/Y H:i') : 'Belum ditentukan'); ?>

            </div>
        </div>
    </div>
</body>

</html>
<?php /**PATH D:\laragon\www\lrp\resources\views\delivery_order\do-preview.blade.php ENDPATH**/ ?>