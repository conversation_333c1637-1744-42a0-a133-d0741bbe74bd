<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - <?php echo e($record->nomor_invoice); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 20px;
        }

        .header {
            width: 100%;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 25%;
            vertical-align: top;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: center;
            padding: 0 10px;
        }

        .invoice-info {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            text-align: right;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-tagline {
            font-size: 9px;
            margin-bottom: 1px;
        }

        .company-contact {
            font-size: 8px;
            margin-top: 5px;
        }

        .invoice-box {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
            min-height: 60px;
        }

        .invoice-label {
            font-size: 9px;
            margin-bottom: 5px;
        }

        .logo-placeholder {
            border: 1px solid #000;
            width: 80px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            text-align: center;
        }

        .title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .invoice-details {
            display: table;
            width: 100%;
            margin-bottom: 15px;
            font-size: 10px;
        }

        .detail-left,
        .detail-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }

        .detail-table {
            width: 100%;
        }

        .detail-table td {
            padding: 2px 5px;
            border: none;
        }

        .detail-table td:first-child {
            font-weight: bold;
            width: 40%;
        }

        .detail-table td:nth-child(2) {
            width: 5%;
            text-align: center;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #000;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }

        .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9px;
        }

        .items-table td {
            min-height: 25px;
        }

        .totals-section {
            float: right;
            width: 300px;
            margin-bottom: 20px;
        }

        .totals-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }

        .totals-table td {
            border: 1px solid #000;
            padding: 5px 8px;
        }

        .totals-table td:first-child {
            font-weight: bold;
            background-color: #f0f0f0;
            text-align: right;
        }

        .totals-table td:last-child {
            text-align: right;
        }

        .total-final {
            background-color: #000 !important;
            color: white !important;
            font-weight: bold !important;
        }

        .notes-section {
            clear: both;
            margin-top: 20px;
            font-size: 10px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .notes-content {
            border: 1px solid #000;
            padding: 10px;
            min-height: 40px;
        }

        .signature-section {
            display: table;
            width: 100%;
            margin-top: 30px;
            font-size: 10px;
        }

        .signature-box {
            display: table-cell;
            width: 50%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .signature-space {
            height: 60px;
            border-bottom: 1px solid #000;
            margin: 10px 0;
        }

        .signature-name {
            margin-top: 5px;
        }

        .page-break {
            page-break-after: always;
        }

        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <?php if(isset($logoBase64) && !empty($logoBase64)): ?>
                    <img src="data:image/png;base64,<?php echo e($logoBase64); ?>" alt="Company Logo" width="80" height="60">
                <?php else: ?>
                    <div class="logo-placeholder">
                        LOGO<br>PERUSAHAAN
                    </div>
                <?php endif; ?>
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div class="company-tagline">MITRA TERPERCAYA & HANDAL</div>
                <div class="company-tagline">Agen BBM - Transportasi BBM - Layanan Bunker</div>
                <div class="company-contact">
                    <?php if($record->letterSetting): ?>
                        <?php echo e($record->letterSetting->phone_number ?? '0761-22369'); ?> -
                        <?php echo e($record->letterSetting->email ?? '<EMAIL>'); ?><br>
                        <?php echo e($record->letterSetting->website ?? 'www.lintasriauprima.com'); ?>

                    <?php else: ?>
                        0761-22369 - <EMAIL><br>
                        www.lintasriauprima.com
                    <?php endif; ?>
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-box">
                    <div class="invoice-label">No. Invoice:</div>
                    <div style="font-weight: bold; font-size: 11px;">
                        <?php echo e($record->nomor_invoice ?? 'N/A'); ?>

                    </div>
                    <div style="margin-top: 5px;">
                        <div class="invoice-label">Status:</div>
                        <div style="font-weight: bold;">
                            <?php echo e(ucfirst($record->status ?? 'draft')); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Title -->
    <div class="title">
        FAKTUR PENJUALAN / INVOICE
    </div>

    <!-- Invoice Details -->
    <div class="invoice-details">
        <div class="detail-left">
            <h4 style="font-size: 11px; margin-bottom: 10px; border-bottom: 1px solid #000; padding-bottom: 3px;">
                Informasi Pelanggan</h4>
            <table class="detail-table">
                <tr>
                    <td>Pelanggan</td>
                    <td>:</td>
                    <td><?php echo e($record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A')); ?></td>
                </tr>
                <tr>
                    <td>Alamat</td>
                    <td>:</td>
                    <td><?php echo e($record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamat ?? 'N/A')); ?>

                    </td>
                </tr>
                <tr>
                    <td>NPWP</td>
                    <td>:</td>
                    <td><?php echo e($record->npwp_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->npwp ?? '-')); ?></td>
                </tr>
            </table>
        </div>
        <div class="detail-right">
            <h4 style="font-size: 11px; margin-bottom: 10px; border-bottom: 1px solid #000; padding-bottom: 3px;">
                Informasi Invoice</h4>
            <table class="detail-table">
                <tr>
                    <td>Tanggal Invoice</td>
                    <td>:</td>
                    <td><?php echo e($record->tanggal_invoice ? $record->tanggal_invoice->format('d M Y') : 'N/A'); ?></td>
                </tr>
                <tr>
                    <td>Jatuh Tempo</td>
                    <td>:</td>
                    <td><?php echo e($record->tanggal_jatuh_tempo ? $record->tanggal_jatuh_tempo->format('d M Y') : 'N/A'); ?></td>
                </tr>
                <tr>
                    <td>Transaksi</td>
                    <td>:</td>
                    <td><?php echo e($record->transaksiPenjualan?->kode ?? 'N/A'); ?></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 45%;">Deskripsi</th>
                <th style="width: 15%;">Jumlah</th>
                <th style="width: 15%;">Harga Satuan</th>
                <th style="width: 20%;">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php
                $itemNumber = 1;
                $subtotalCalculated = 0;
            ?>

            <?php if($record->invoiceItems && $record->invoiceItems->count() > 0): ?>
                <?php $__currentLoopData = $record->invoiceItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $total = $item->quantity * $item->unit_price;
                        $subtotalCalculated += $total;
                    ?>
                    <tr>
                        <td><?php echo e($itemNumber++); ?></td>
                        <td style="text-align: left; padding-left: 8px;">
                            <?php echo e($item->description ?? ($item->item?->name ?? 'N/A')); ?>

                        </td>
                        <td><?php echo e(number_format($item->quantity ?? 0, 0, ',', '.')); ?> <?php echo e($item->unit ?? 'L'); ?></td>
                        <td><?php echo e($record->formatCurrency($item->unit_price ?? 0)); ?></td>
                        <td><?php echo e($record->formatCurrency($total)); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <?php if($record->transaksiPenjualan && $record->transaksiPenjualan->penjualanDetails): ?>
                    <?php $__currentLoopData = $record->transaksiPenjualan->penjualanDetails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $total = $detail->volume_item * $detail->harga_jual;
                            $subtotalCalculated += $total;
                        ?>
                        <tr>
                            <td><?php echo e($itemNumber++); ?></td>
                            <td style="text-align: left; padding-left: 8px;">
                                <?php echo e($detail->item->name ?? 'N/A'); ?>

                            </td>
                            <td><?php echo e(number_format($detail->volume_item ?? 0, 0, ',', '.')); ?> L</td>
                            <td><?php echo e($record->formatCurrency($detail->harga_jual ?? 0)); ?></td>
                            <td><?php echo e($record->formatCurrency($total)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Empty rows for manual filling -->
            <?php for($i = $itemNumber; $i <= 5; $i++): ?>
                <tr>
                    <td><?php echo e($i); ?></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            <?php endfor; ?>
        </tbody>
    </table>

    <!-- Totals Section -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td>Subtotal</td>
                <td><?php echo e($record->formatCurrency($record->subtotal ?? $subtotalCalculated)); ?></td>
            </tr>
            <?php if($record->include_ppn): ?>
                <tr>
                    <td>PPN 11%</td>
                    <td><?php echo e($record->formatCurrency($record->total_pajak ?? 0)); ?></td>
                </tr>
            <?php endif; ?>
            <?php if($record->include_operasional_kerja && $record->biaya_operasional_kerja > 0): ?>
                <tr>
                    <td>Biaya Operasional</td>
                    <td><?php echo e($record->formatCurrency($record->biaya_operasional_kerja ?? 0)); ?></td>
                </tr>
            <?php endif; ?>
            <?php if($record->include_pbbkb && $record->biaya_pbbkb > 0): ?>
                <tr>
                    <td>Biaya PBBKB</td>
                    <td><?php echo e($record->formatCurrency($record->biaya_pbbkb ?? 0)); ?></td>
                </tr>
            <?php endif; ?>
            <tr class="total-final">
                <td>Total Invoice</td>
                <td><?php echo e($record->formatCurrency($record->total_invoice ?? ($record->calculated_total ?? 0))); ?></td>
            </tr>
        </table>
    </div>

    <!-- Notes Section -->
    <div class="notes-section">
        <div class="notes-title">Catatan:</div>
        <div class="notes-content">
            <?php echo e($record->catatan ?? ''); ?>

        </div>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-title">Penerbit</div>
            <div class="signature-space"></div>
            <div class="signature-name">
                <strong>PT. LINTAS RIAU PRIMA</strong><br>
                Authorized Signature
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-title">Penerima</div>
            <div class="signature-space"></div>
            <div class="signature-name">
                <strong><?php echo e($record->nama_pelanggan ?? 'Customer Name'); ?></strong><br>
                Nama & Tanda Tangan
            </div>
        </div>
    </div>

    <!-- Footer with company address -->
    <div style="margin-top: 30px; text-align: center; font-size: 9px; border-top: 1px solid #000; padding-top: 10px;">
        <strong>PT. LINTAS RIAU PRIMA</strong><br>
        <?php if($record->letterSetting): ?>
            <?php echo e($record->letterSetting->address); ?><br>
            Telp: <?php echo e($record->letterSetting->phone_number); ?> | Email: <?php echo e($record->letterSetting->email); ?> | Website:
            <?php echo e($record->letterSetting->website); ?>

        <?php else: ?>
            Jl. Raya Lintas Timur KM 16, Kelurahan Tuah Karya, Kecamatan Tampan, Kota Pekanbaru, Riau 28293<br>
            Telp: 0761-22369 | Email: <EMAIL> | Website: www.lintasriauprima.com
        <?php endif; ?>
    </div>
</body>

</html>
<?php /**PATH D:\laragon\www\lrp\resources\views\pdf\invoice_id.blade.php ENDPATH**/ ?>