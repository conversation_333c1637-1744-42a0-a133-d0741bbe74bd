<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.resources.absensi-resource.pages.create-absensi' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\CreateAbsensi',
    'app.filament.resources.absensi-resource.pages.edit-absensi' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\EditAbsensi',
    'app.filament.resources.absensi-resource.pages.list-absensis' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\ListAbsensis',
    'app.filament.resources.absensi-resource.pages.view-absensi' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\ViewAbsensi',
    'app.filament.resources.akun-resource.pages.create-akun' => 'App\\Filament\\Resources\\AkunResource\\Pages\\CreateAkun',
    'app.filament.resources.akun-resource.pages.edit-akun' => 'App\\Filament\\Resources\\AkunResource\\Pages\\EditAkun',
    'app.filament.resources.akun-resource.pages.list-akuns' => 'App\\Filament\\Resources\\AkunResource\\Pages\\ListAkuns',
    'app.filament.resources.alamat-pelanggan-resource.pages.create-alamat-pelanggan' => 'App\\Filament\\Resources\\AlamatPelangganResource\\Pages\\CreateAlamatPelanggan',
    'app.filament.resources.alamat-pelanggan-resource.pages.edit-alamat-pelanggan' => 'App\\Filament\\Resources\\AlamatPelangganResource\\Pages\\EditAlamatPelanggan',
    'app.filament.resources.alamat-pelanggan-resource.pages.list-alamat-pelanggans' => 'App\\Filament\\Resources\\AlamatPelangganResource\\Pages\\ListAlamatPelanggans',
    'app.filament.resources.delivery-order-resource.pages.create-delivery-order' => 'App\\Filament\\Resources\\DeliveryOrderResource\\Pages\\CreateDeliveryOrder',
    'app.filament.resources.delivery-order-resource.pages.edit-delivery-order' => 'App\\Filament\\Resources\\DeliveryOrderResource\\Pages\\EditDeliveryOrder',
    'app.filament.resources.delivery-order-resource.pages.list-delivery-orders' => 'App\\Filament\\Resources\\DeliveryOrderResource\\Pages\\ListDeliveryOrders',
    'app.filament.resources.delivery-order-resource.pages.view-delivery-order' => 'App\\Filament\\Resources\\DeliveryOrderResource\\Pages\\ViewDeliveryOrder',
    'app.filament.resources.expense-request-resource.pages.create-expense-request' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\CreateExpenseRequest',
    'app.filament.resources.expense-request-resource.pages.edit-expense-request' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\EditExpenseRequest',
    'app.filament.resources.expense-request-resource.pages.list-expense-requests' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\ListExpenseRequests',
    'app.filament.resources.expense-request-resource.pages.view-expense-request' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\ViewExpenseRequest',
    'app.filament.resources.faktur-pajak-resource.pages.create-faktur-pajak' => 'App\\Filament\\Resources\\FakturPajakResource\\Pages\\CreateFakturPajak',
    'app.filament.resources.faktur-pajak-resource.pages.edit-faktur-pajak' => 'App\\Filament\\Resources\\FakturPajakResource\\Pages\\EditFakturPajak',
    'app.filament.resources.faktur-pajak-resource.pages.list-faktur-pajaks' => 'App\\Filament\\Resources\\FakturPajakResource\\Pages\\ListFakturPajaks',
    'app.filament.resources.inventory-resource.pages.create-inventory' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\CreateInventory',
    'app.filament.resources.inventory-resource.pages.edit-inventory' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\EditInventory',
    'app.filament.resources.inventory-resource.pages.list-inventories' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\ListInventories',
    'app.filament.resources.inventory-resource.pages.view-inventory' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\ViewInventory',
    'app.filament.resources.invoice-resource.pages.create-invoice' => 'App\\Filament\\Resources\\InvoiceResource\\Pages\\CreateInvoice',
    'app.filament.resources.invoice-resource.pages.edit-invoice' => 'App\\Filament\\Resources\\InvoiceResource\\Pages\\EditInvoice',
    'app.filament.resources.invoice-resource.pages.list-invoices' => 'App\\Filament\\Resources\\InvoiceResource\\Pages\\ListInvoices',
    'app.filament.resources.invoice-resource.pages.view-invoice' => 'App\\Filament\\Resources\\InvoiceResource\\Pages\\ViewInvoice',
    'app.filament.resources.iso-certification-resource.pages.create-iso-certification' => 'App\\Filament\\Resources\\IsoCertificationResource\\Pages\\CreateIsoCertification',
    'app.filament.resources.iso-certification-resource.pages.edit-iso-certification' => 'App\\Filament\\Resources\\IsoCertificationResource\\Pages\\EditIsoCertification',
    'app.filament.resources.iso-certification-resource.pages.list-iso-certifications' => 'App\\Filament\\Resources\\IsoCertificationResource\\Pages\\ListIsoCertifications',
    'app.filament.resources.iso-certification-resource.pages.view-iso-certification' => 'App\\Filament\\Resources\\IsoCertificationResource\\Pages\\ViewIsoCertification',
    'app.filament.resources.item-resource.pages.create-item' => 'App\\Filament\\Resources\\ItemResource\\Pages\\CreateItem',
    'app.filament.resources.item-resource.pages.edit-item' => 'App\\Filament\\Resources\\ItemResource\\Pages\\EditItem',
    'app.filament.resources.item-resource.pages.list-items' => 'App\\Filament\\Resources\\ItemResource\\Pages\\ListItems',
    'app.filament.resources.jabatan-resource.pages.edit-jabatan' => 'App\\Filament\\Resources\\JabatanResource\\Pages\\EditJabatan',
    'app.filament.resources.journal-resource.pages.create-journal' => 'App\\Filament\\Resources\\JournalResource\\Pages\\CreateJournal',
    'app.filament.resources.journal-resource.pages.edit-journal' => 'App\\Filament\\Resources\\JournalResource\\Pages\\EditJournal',
    'app.filament.resources.journal-resource.pages.list-journals' => 'App\\Filament\\Resources\\JournalResource\\Pages\\ListJournals',
    'app.filament.resources.journal-resource.pages.view-journal' => 'App\\Filament\\Resources\\JournalResource\\Pages\\ViewJournal',
    'app.filament.resources.kendaraan-resource.pages.create-kendaraan' => 'App\\Filament\\Resources\\KendaraanResource\\Pages\\CreateKendaraan',
    'app.filament.resources.kendaraan-resource.pages.edit-kendaraan' => 'App\\Filament\\Resources\\KendaraanResource\\Pages\\EditKendaraan',
    'app.filament.resources.kendaraan-resource.pages.list-kendaraans' => 'App\\Filament\\Resources\\KendaraanResource\\Pages\\ListKendaraans',
    'app.filament.resources.letter-setting-resource.pages.create-letter-setting' => 'App\\Filament\\Resources\\LetterSettingResource\\Pages\\CreateLetterSetting',
    'app.filament.resources.letter-setting-resource.pages.edit-letter-setting' => 'App\\Filament\\Resources\\LetterSettingResource\\Pages\\EditLetterSetting',
    'app.filament.resources.letter-setting-resource.pages.list-letter-settings' => 'App\\Filament\\Resources\\LetterSettingResource\\Pages\\ListLetterSettings',
    'app.filament.resources.letter-setting-resource.pages.view-letter-setting' => 'App\\Filament\\Resources\\LetterSettingResource\\Pages\\ViewLetterSetting',
    'app.filament.resources.notification-setting-resource.pages.create-notification-setting' => 'App\\Filament\\Resources\\NotificationSettingResource\\Pages\\CreateNotificationSetting',
    'app.filament.resources.notification-setting-resource.pages.edit-notification-setting' => 'App\\Filament\\Resources\\NotificationSettingResource\\Pages\\EditNotificationSetting',
    'app.filament.resources.notification-setting-resource.pages.list-notification-settings' => 'App\\Filament\\Resources\\NotificationSettingResource\\Pages\\ListNotificationSettings',
    'app.filament.resources.notification-setting-resource.pages.view-notification-setting' => 'App\\Filament\\Resources\\NotificationSettingResource\\Pages\\ViewNotificationSetting',
    'app.filament.resources.numbering-setting-resource.pages.create-numbering-setting' => 'App\\Filament\\Resources\\NumberingSettingResource\\Pages\\CreateNumberingSetting',
    'app.filament.resources.numbering-setting-resource.pages.edit-numbering-setting' => 'App\\Filament\\Resources\\NumberingSettingResource\\Pages\\EditNumberingSetting',
    'app.filament.resources.numbering-setting-resource.pages.list-numbering-settings' => 'App\\Filament\\Resources\\NumberingSettingResource\\Pages\\ListNumberingSettings',
    'app.filament.resources.numbering-setting-resource.pages.view-numbering-setting' => 'App\\Filament\\Resources\\NumberingSettingResource\\Pages\\ViewNumberingSetting',
    'app.filament.resources.payment-method-resource.pages.create-payment-method' => 'App\\Filament\\Resources\\PaymentMethodResource\\Pages\\CreatePaymentMethod',
    'app.filament.resources.payment-method-resource.pages.edit-payment-method' => 'App\\Filament\\Resources\\PaymentMethodResource\\Pages\\EditPaymentMethod',
    'app.filament.resources.payment-method-resource.pages.list-payment-methods' => 'App\\Filament\\Resources\\PaymentMethodResource\\Pages\\ListPaymentMethods',
    'app.filament.resources.payment-method-resource.pages.view-payment-method' => 'App\\Filament\\Resources\\PaymentMethodResource\\Pages\\ViewPaymentMethod',
    'app.filament.resources.pelanggan-resource.pages.create-pelanggan' => 'App\\Filament\\Resources\\PelangganResource\\Pages\\CreatePelanggan',
    'app.filament.resources.pelanggan-resource.pages.edit-pelanggan' => 'App\\Filament\\Resources\\PelangganResource\\Pages\\EditPelanggan',
    'app.filament.resources.pelanggan-resource.pages.list-pelanggans' => 'App\\Filament\\Resources\\PelangganResource\\Pages\\ListPelanggans',
    'app.filament.resources.pengiriman-driver-resource.pages.create-pengiriman-driver' => 'App\\Filament\\Resources\\PengirimanDriverResource\\Pages\\CreatePengirimanDriver',
    'app.filament.resources.pengiriman-driver-resource.pages.edit-pengiriman-driver' => 'App\\Filament\\Resources\\PengirimanDriverResource\\Pages\\EditPengirimanDriver',
    'app.filament.resources.pengiriman-driver-resource.pages.list-pengiriman-drivers' => 'App\\Filament\\Resources\\PengirimanDriverResource\\Pages\\ListPengirimanDrivers',
    'app.filament.resources.pengiriman-driver-resource.pages.view-pengiriman-driver' => 'App\\Filament\\Resources\\PengirimanDriverResource\\Pages\\ViewPengirimanDriver',
    'app.filament.resources.posting-rule-resource.pages.create-posting-rule' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\CreatePostingRule',
    'app.filament.resources.posting-rule-resource.pages.edit-posting-rule' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\EditPostingRule',
    'app.filament.resources.posting-rule-resource.pages.list-posting-rules' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\ListPostingRules',
    'app.filament.resources.posting-rule-resource.pages.view-posting-rule' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\ViewPostingRule',
    'app.filament.resources.province-resource.pages.create-province' => 'App\\Filament\\Resources\\ProvinceResource\\Pages\\CreateProvince',
    'app.filament.resources.province-resource.pages.edit-province' => 'App\\Filament\\Resources\\ProvinceResource\\Pages\\EditProvince',
    'app.filament.resources.province-resource.pages.list-provinces' => 'App\\Filament\\Resources\\ProvinceResource\\Pages\\ListProvinces',
    'app.filament.resources.province-resource.pages.view-province' => 'App\\Filament\\Resources\\ProvinceResource\\Pages\\ViewProvince',
    'app.filament.resources.province-resource.relation-managers.districts-relation-manager' => 'App\\Filament\\Resources\\ProvinceResource\\RelationManagers\\DistrictsRelationManager',
    'app.filament.resources.province-resource.relation-managers.regencies-relation-manager' => 'App\\Filament\\Resources\\ProvinceResource\\RelationManagers\\RegenciesRelationManager',
    'app.filament.resources.province-resource.relation-managers.subdistricts-relation-manager' => 'App\\Filament\\Resources\\ProvinceResource\\RelationManagers\\SubdistrictsRelationManager',
    'app.filament.resources.receipt-resource.pages.create-receipt' => 'App\\Filament\\Resources\\ReceiptResource\\Pages\\CreateReceipt',
    'app.filament.resources.receipt-resource.pages.edit-receipt' => 'App\\Filament\\Resources\\ReceiptResource\\Pages\\EditReceipt',
    'app.filament.resources.receipt-resource.pages.list-receipts' => 'App\\Filament\\Resources\\ReceiptResource\\Pages\\ListReceipts',
    'app.filament.resources.receipt-resource.pages.view-receipt' => 'App\\Filament\\Resources\\ReceiptResource\\Pages\\ViewReceipt',
    'app.filament.resources.role-resource.pages.create-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\CreateRole',
    'app.filament.resources.role-resource.pages.edit-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\EditRole',
    'app.filament.resources.role-resource.pages.list-roles' => 'App\\Filament\\Resources\\RoleResource\\Pages\\ListRoles',
    'app.filament.resources.role-resource.pages.view-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\ViewRole',
    'app.filament.resources.schedule-resource.pages.create-schedule' => 'App\\Filament\\Resources\\ScheduleResource\\Pages\\CreateSchedule',
    'app.filament.resources.schedule-resource.pages.edit-schedule' => 'App\\Filament\\Resources\\ScheduleResource\\Pages\\EditSchedule',
    'app.filament.resources.schedule-resource.pages.list-schedules' => 'App\\Filament\\Resources\\ScheduleResource\\Pages\\ListSchedules',
    'app.filament.resources.schedule-resource.pages.view-schedule' => 'App\\Filament\\Resources\\ScheduleResource\\Pages\\ViewSchedule',
    'app.filament.resources.shift-resource.pages.create-shift' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\CreateShift',
    'app.filament.resources.shift-resource.pages.edit-shift' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\EditShift',
    'app.filament.resources.shift-resource.pages.list-shifts' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\ListShifts',
    'app.filament.resources.shift-resource.pages.view-shift' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\ViewShift',
    'app.filament.resources.sph-resource.pages.create-sph' => 'App\\Filament\\Resources\\SphResource\\Pages\\CreateSph',
    'app.filament.resources.sph-resource.pages.edit-sph' => 'App\\Filament\\Resources\\SphResource\\Pages\\EditSph',
    'app.filament.resources.sph-resource.pages.list-sphs' => 'App\\Filament\\Resources\\SphResource\\Pages\\ListSphs',
    'app.filament.resources.sph-resource.pages.view-sph' => 'App\\Filament\\Resources\\SphResource\\Pages\\ViewSph',
    'app.filament.resources.supplier-resource.pages.create-supplier' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\CreateSupplier',
    'app.filament.resources.supplier-resource.pages.edit-supplier' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\EditSupplier',
    'app.filament.resources.supplier-resource.pages.list-suppliers' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\ListSuppliers',
    'app.filament.resources.tax-invoice-resource.pages.create-tax-invoice' => 'App\\Filament\\Resources\\TaxInvoiceResource\\Pages\\CreateTaxInvoice',
    'app.filament.resources.tax-invoice-resource.pages.edit-tax-invoice' => 'App\\Filament\\Resources\\TaxInvoiceResource\\Pages\\EditTaxInvoice',
    'app.filament.resources.tax-invoice-resource.pages.list-tax-invoices' => 'App\\Filament\\Resources\\TaxInvoiceResource\\Pages\\ListTaxInvoices',
    'app.filament.resources.tax-invoice-resource.pages.view-tax-invoice' => 'App\\Filament\\Resources\\TaxInvoiceResource\\Pages\\ViewTaxInvoice',
    'app.filament.resources.tbbm-resource.pages.create-tbbm' => 'App\\Filament\\Resources\\TbbmResource\\Pages\\CreateTbbm',
    'app.filament.resources.tbbm-resource.pages.edit-tbbm' => 'App\\Filament\\Resources\\TbbmResource\\Pages\\EditTbbm',
    'app.filament.resources.tbbm-resource.pages.list-tbbms' => 'App\\Filament\\Resources\\TbbmResource\\Pages\\ListTbbms',
    'app.filament.resources.transaksi-penjualan-resource.pages.create-transaksi-penjualan' => 'App\\Filament\\Resources\\TransaksiPenjualanResource\\Pages\\CreateTransaksiPenjualan',
    'app.filament.resources.transaksi-penjualan-resource.pages.edit-transaksi-penjualan' => 'App\\Filament\\Resources\\TransaksiPenjualanResource\\Pages\\EditTransaksiPenjualan',
    'app.filament.resources.transaksi-penjualan-resource.pages.list-transaksi-penjualans' => 'App\\Filament\\Resources\\TransaksiPenjualanResource\\Pages\\ListTransaksiPenjualans',
    'app.filament.resources.transaksi-penjualan-resource.pages.view-transaksi-penjualan' => 'App\\Filament\\Resources\\TransaksiPenjualanResource\\Pages\\ViewTransaksiPenjualan',
    'app.filament.resources.transaksi-penjualan-resource.relation-managers.delivery-orders-relation-manager' => 'App\\Filament\\Resources\\TransaksiPenjualanResource\\RelationManagers\\DeliveryOrdersRelationManager',
    'app.filament.resources.uang-jalan-resource.pages.create-uang-jalan' => 'App\\Filament\\Resources\\UangJalanResource\\Pages\\CreateUangJalan',
    'app.filament.resources.uang-jalan-resource.pages.edit-uang-jalan' => 'App\\Filament\\Resources\\UangJalanResource\\Pages\\EditUangJalan',
    'app.filament.resources.uang-jalan-resource.pages.list-uang-jalans' => 'App\\Filament\\Resources\\UangJalanResource\\Pages\\ListUangJalans',
    'app.filament.resources.uang-jalan-resource.pages.view-uang-jalan' => 'App\\Filament\\Resources\\UangJalanResource\\Pages\\ViewUangJalan',
    'app.filament.resources.user-resource.pages.create-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\CreateUser',
    'app.filament.resources.user-resource.pages.edit-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\EditUser',
    'app.filament.resources.user-resource.pages.list-users' => 'App\\Filament\\Resources\\UserResource\\Pages\\ListUsers',
    'app.filament.resources.user-resource.pages.view-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\ViewUser',
    'app.filament.pages.accounts-receivable-dashboard' => 'App\\Filament\\Pages\\AccountsReceivableDashboard',
    'app.filament.pages.balance-sheet' => 'App\\Filament\\Pages\\BalanceSheet',
    'app.filament.pages.driver-dashboard' => 'App\\Filament\\Pages\\DriverDashboard',
    'app.filament.pages.driver-delivery-detail' => 'App\\Filament\\Pages\\DriverDeliveryDetail',
    'app.filament.pages.fuel-delivery-dashboard' => 'App\\Filament\\Pages\\FuelDeliveryDashboard',
    'app.filament.pages.general-ledger' => 'App\\Filament\\Pages\\GeneralLedger',
    'app.filament.pages.income-statement' => 'App\\Filament\\Pages\\IncomeStatement',
    'app.filament.pages.media-manager' => 'App\\Filament\\Pages\\MediaManager',
    'app.filament.pages.monthly-delivery-report-dashboard' => 'App\\Filament\\Pages\\MonthlyDeliveryReportDashboard',
    'app.filament.pages.monthly-sales-realization-dashboard' => 'App\\Filament\\Pages\\MonthlySalesRealizationDashboard',
    'app.filament.pages.sales-order-timeline' => 'App\\Filament\\Pages\\SalesOrderTimeline',
    'app.filament.pages.sales-order-timeline-detail' => 'App\\Filament\\Pages\\SalesOrderTimelineDetail',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.widgets.filament-info-widget' => 'Filament\\Widgets\\FilamentInfoWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\AccountsReceivableDashboard.php' => 'App\\Filament\\Pages\\AccountsReceivableDashboard',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\BalanceSheet.php' => 'App\\Filament\\Pages\\BalanceSheet',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\DriverDashboard.php' => 'App\\Filament\\Pages\\DriverDashboard',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\DriverDeliveryDetail.php' => 'App\\Filament\\Pages\\DriverDeliveryDetail',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\FuelDeliveryDashboard.php' => 'App\\Filament\\Pages\\FuelDeliveryDashboard',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\GeneralLedger.php' => 'App\\Filament\\Pages\\GeneralLedger',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\IncomeStatement.php' => 'App\\Filament\\Pages\\IncomeStatement',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\MediaManager.php' => 'App\\Filament\\Pages\\MediaManager',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\MonthlyDeliveryReportDashboard.php' => 'App\\Filament\\Pages\\MonthlyDeliveryReportDashboard',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\MonthlySalesRealizationDashboard.php' => 'App\\Filament\\Pages\\MonthlySalesRealizationDashboard',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\SalesOrderTimeline.php' => 'App\\Filament\\Pages\\SalesOrderTimeline',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Pages\\SalesOrderTimelineDetail.php' => 'App\\Filament\\Pages\\SalesOrderTimelineDetail',
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\lrp\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\AbsensiResource.php' => 'App\\Filament\\Resources\\AbsensiResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\AkunResource.php' => 'App\\Filament\\Resources\\AkunResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\AlamatPelangganResource.php' => 'App\\Filament\\Resources\\AlamatPelangganResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\DeliveryOrderResource.php' => 'App\\Filament\\Resources\\DeliveryOrderResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\ExpenseRequestResource.php' => 'App\\Filament\\Resources\\ExpenseRequestResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\FakturPajakResource.php' => 'App\\Filament\\Resources\\FakturPajakResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\InventoryResource.php' => 'App\\Filament\\Resources\\InventoryResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\InvoiceResource.php' => 'App\\Filament\\Resources\\InvoiceResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\IsoCertificationResource.php' => 'App\\Filament\\Resources\\IsoCertificationResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\ItemResource.php' => 'App\\Filament\\Resources\\ItemResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\JournalResource.php' => 'App\\Filament\\Resources\\JournalResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\KendaraanResource.php' => 'App\\Filament\\Resources\\KendaraanResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\LetterSettingResource.php' => 'App\\Filament\\Resources\\LetterSettingResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\NotificationSettingResource.php' => 'App\\Filament\\Resources\\NotificationSettingResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\NumberingSettingResource.php' => 'App\\Filament\\Resources\\NumberingSettingResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\PaymentMethodResource.php' => 'App\\Filament\\Resources\\PaymentMethodResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\PelangganResource.php' => 'App\\Filament\\Resources\\PelangganResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\PengirimanDriverResource.php' => 'App\\Filament\\Resources\\PengirimanDriverResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\PostingRuleResource.php' => 'App\\Filament\\Resources\\PostingRuleResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\ProvinceResource.php' => 'App\\Filament\\Resources\\ProvinceResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\ReceiptResource.php' => 'App\\Filament\\Resources\\ReceiptResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\RoleResource.php' => 'App\\Filament\\Resources\\RoleResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\ScheduleResource.php' => 'App\\Filament\\Resources\\ScheduleResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\ShiftResource.php' => 'App\\Filament\\Resources\\ShiftResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\SphResource.php' => 'App\\Filament\\Resources\\SphResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\SupplierResource.php' => 'App\\Filament\\Resources\\SupplierResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\TaxInvoiceResource.php' => 'App\\Filament\\Resources\\TaxInvoiceResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\TbbmResource.php' => 'App\\Filament\\Resources\\TbbmResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\TransaksiPenjualanResource.php' => 'App\\Filament\\Resources\\TransaksiPenjualanResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\UangJalanResource.php' => 'App\\Filament\\Resources\\UangJalanResource',
    'D:\\laragon\\www\\lrp\\app\\Filament\\Resources\\UserResource.php' => 'App\\Filament\\Resources\\UserResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\lrp\\app\\Filament/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
    1 => 'Filament\\Widgets\\FilamentInfoWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\lrp\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);