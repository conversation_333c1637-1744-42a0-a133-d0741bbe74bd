<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DeliveryOrderResource\Pages;
use App\Filament\Resources\DeliveryOrderResource\RelationManagers;
use App\Models\DeliveryOrder;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\File;


class DeliveryOrderResource extends Resource
{
    protected static ?string $model = DeliveryOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Operasional';

    protected static ?string $navigationLabel = 'Delivery Order';

    protected static ?int $navigationSort = 1;

    protected function calculateTotalVolume(callable $get, callable $set): void
    {
        $idTransaksi = $get('id_transaksi');
        if (!$idTransaksi) return;

        $transaksi = \App\Models\TransaksiPenjualan::with('penjualanDetails')->find($idTransaksi);
        if (!$transaksi) return;

        $totalVolume = $transaksi->penjualanDetails->sum('volume_do');
        $set('volume_do', $totalVolume);

        // Calculate remaining volume
        $totalSoVolume = $transaksi->penjualanDetails->sum('volume_item');
        $set('sisa_volume_do', max(0, $totalSoVolume - $totalVolume));
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Delivery Order')
                    ->schema([
                        Forms\Components\TextInput::make('kode')
                            ->label('Nomor DO')
                            ->placeholder('Auto-generated')
                            ->disabled()
                            ->helperText('Nomor DO akan otomatis dibuat saat menyimpan')
                            ->dehydrated()
                            ->maxLength(50),

                        Forms\Components\Select::make('id_transaksi')
                            ->label('Transaksi Penjualan')
                            ->placeholder('Pilih Transaksi Penjualan')
                            ->relationship('transaksi', 'id')
                            ->getOptionLabelFromRecordUsing(fn($record) => $record->nomor_transaksi . ' - ' . $record->pelanggan->nama)
                            ->searchable(['id'])
                            ->helperText('Pilih transaksi penjualan yang akan dijadwalkan pengirimannya')
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                // Auto-calculate remaining volume when SO changes
                                if ($state) {
                                    $transaksi = \App\Models\TransaksiPenjualan::with(['penjualanDetails.item', 'sph.letterSetting'])->find($state);
                                    if ($transaksi) {
                                        $totalSoVolume = $transaksi->penjualanDetails->sum('volume_item');
                                        $deliveredVolume = \App\Models\DeliveryOrder::where('id_transaksi', $state)
                                            ->where('id', '!=', $get('id') ?? 0)
                                            ->sum('volume_do');

                                        // Include current DO volume in calculation
                                        $currentVolume = floatval($get('volume_do') ?? 0);
                                        $remainingVolume = $totalSoVolume - $deliveredVolume - $currentVolume;
                                        $set('sisa_volume_do', $remainingVolume);

                                        // Auto-populate volume details from SO items if not set
                                        if (!$get('volume_details') || empty($get('volume_details'))) {
                                            $volumeDetails = [];
                                            foreach ($transaksi->penjualanDetails as $detail) {
                                                if ($detail->item) {
                                                    $volumeDetails[] = [
                                                        'id_item' => $detail->id_item,
                                                        'item_name' => $detail->item->nama_item,
                                                        'volume' => $detail->volume_item,
                                                        'max_volume' => $detail->volume_item,
                                                        'keterangan' => 'Auto-filled dari SO',
                                                    ];
                                                }
                                            }
                                            $set('volume_details', $volumeDetails);
                                            $set('volume_do', $totalSoVolume);
                                        }

                                        // Auto-fill letter setting from SPH or default
                                        if ($transaksi->sph && $transaksi->sph->letterSetting) {
                                            // If SO has SPH and SPH has letter setting, use it
                                            $set('letter_setting_id', $transaksi->sph->letter_setting_id);
                                        } else {
                                            // Otherwise, use default letter setting
                                            $defaultLetterSetting = \App\Models\LetterSetting::where('is_default', true)->first();
                                            if ($defaultLetterSetting) {
                                                $set('letter_setting_id', $defaultLetterSetting->id);
                                            }
                                        }
                                    }
                                }
                            })
                            ->default(function () {
                                // Autofill from URL parameter
                                return request()->query('id_transaksi', null);
                            })
                            ->required(),

                        Forms\Components\Select::make('letter_setting_id')
                            ->label('Penerbitan Surat')
                            ->relationship('letterSetting', 'name', fn($query) => $query->where('is_active', true))
                            ->searchable()
                            ->preload()
                            ->helperText('Format surat akan diambil otomatis dari SPH atau menggunakan default')
                            ->required(),

                        Forms\Components\Select::make('id_user')
                            ->label('Supir')
                            ->required()
                            ->placeholder('Pilih Supir')
                            ->helperText('Pilih supir yang akan mengantar barang')
                            ->relationship(
                                name: 'user',
                                titleAttribute: 'name',
                                modifyQueryUsing: function ($query) {
                                    $query->whereHas('jabatan', function ($query) {
                                        $query->where('nama', 'like', '%driver%');
                                    });
                                }
                            )
                            ->searchable()
                            ->preload(),

                        // !todo : tambah keterangan nama kendaraan
                        Forms\Components\Select::make('id_kendaraan')
                            ->label('Kendaraan')
                            ->required()
                            ->placeholder('Pilih Kendaraan')
                            ->helperText('Pilih kendaraan yang akan mengantar barang')
                            ->relationship('kendaraan', 'no_pol_kendaraan')
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ]),

                        Forms\Components\Section::make('Detail Item Pengiriman')
                            ->description('Detail item yang akan dikirim dalam delivery order ini')
                            ->schema([
                                Forms\Components\Repeater::make('details')
                                    ->label('Detail Item')
                                    ->relationship('details')
                                    ->schema([
                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Select::make('id_penjualan_detail')
                                                    ->label('Item dari Transaksi')
                                                    ->options(function (callable $get) {
                                                        $transaksiId = $get('../../id_transaksi');
                                                        if (!$transaksiId) return [];

                                                        return \App\Models\PenjualanDetail::where('id_transaksi_penjualan', $transaksiId)
                                                            ->with('item')
                                                            ->get()
                                                            ->mapWithKeys(function ($detail) {
                                                                return [$detail->id => $detail->item->name . ' - ' . number_format($detail->volume_item, 0, ',', '.') . ' L'];
                                                            });
                                                    })
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, $state) {
                                                        if ($state) {
                                                            $penjualanDetail = \App\Models\PenjualanDetail::with('item.satuan')->find($state);
                                                            if ($penjualanDetail) {
                                                                $set('id_item', $penjualanDetail->id_item);
                                                                $set('item_name', $penjualanDetail->item->name);
                                                                $set('item_description', $penjualanDetail->item->description);
                                                                $set('volume_ordered', $penjualanDetail->volume_item);
                                                                $set('unit', $penjualanDetail->item->satuan->nama ?? 'Liter');
                                                                $set('unit_price', $penjualanDetail->harga_jual);

                                                                // Set default volume_delivered to volume_ordered
                                                                $set('volume_delivered', $penjualanDetail->volume_item);

                                                                // Calculate total amount
                                                                $totalAmount = $penjualanDetail->volume_item * $penjualanDetail->harga_jual;
                                                                $set('total_amount', $totalAmount);
                                                            }
                                                        }
                                                    })
                                                    ->required(),

                                                Forms\Components\TextInput::make('volume_ordered')
                                                    ->label('Volume Dipesan')
                                                    ->numeric()
                                                    ->suffix('L')
                                                    ->disabled(),

                                                Forms\Components\TextInput::make('volume_delivered')
                                                    ->label('Volume Dikirim')
                                                    ->numeric()
                                                    ->suffix('L')
                                                    ->required()
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        $volume = (float) $get('volume_delivered');
                                                        $price = (float) $get('unit_price');
                                                        $set('total_amount', $volume * $price);
                                                    }),
                                            ]),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\TextInput::make('unit_price')
                                                    ->label('Harga Satuan')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled(),

                                                Forms\Components\TextInput::make('total_amount')
                                                    ->label('Total')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled(),

                                                Forms\Components\TextInput::make('unit')
                                                    ->label('Satuan')
                                                    ->disabled(),
                                            ]),

                                        Forms\Components\Textarea::make('notes')
                                            ->label('Catatan')
                                            ->rows(2)
                                            ->columnSpanFull(),

                                        Forms\Components\Hidden::make('id_item'),
                                        Forms\Components\Hidden::make('item_name'),
                                        Forms\Components\Hidden::make('item_description'),
                                    ])
                                    ->defaultItems(1)
                                    ->addActionLabel('Tambah Item')
                                    ->collapsible()
                                    ->cloneable()
                                    ->columnSpanFull(),
                            ])
                            ->visible(fn(callable $get) => !empty($get('id_transaksi')))
                            ->collapsible(),

                        Forms\Components\TextInput::make('volume_do')
                            ->label('Total Volume DO')
                            ->placeholder('Total akan otomatis terhitung dari detail items')
                            ->helperText('Total volume dari semua detail items (otomatis terhitung)')
                            ->numeric()
                            ->suffix('L')
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\TextInput::make('sisa_volume_do')
                            ->label('Sisa Volume DO')
                            ->placeholder('Sisa volume akan otomatis terhitung')
                            ->helperText('Sisa volume dari SO yang belum dikirim (otomatis terhitung)')
                            ->numeric()
                            ->suffix('L')
                            ->disabled()
                            ->dehydrated(),

                    ])
                    ->columns(2),

                Forms\Components\Section::make('Informasi Muat')
                    ->description('Informasi ini akan diisi oleh supir setelah barang dikirim')
                    ->schema([
                        Forms\Components\Select::make('status_muat')
                            ->label('Status Muat')
                            ->disabled()
                            ->helperText('Status ini akan diisi oleh supir setiap perubahan status muat')
                            ->options([
                                'pending' => 'Perintah Muat Diterbitkan',
                                'muat' => 'Muat Dikonfirmasi',
                                'selesai' => 'Muat Selesai',
                            ])
                            ->default('pending'),

                        Forms\Components\DateTimePicker::make('waktu_muat')
                            ->disabled()
                            ->helperText('Waktu mulai muat akan automatis terisi oleh supir setelah barang dikirim')
                            ->label('Waktu Mulai Muat'),

                        Forms\Components\DateTimePicker::make('waktu_selesai_muat')
                            ->disabled()
                            ->helperText('Waktu selesai muat akan automatis terisi oleh supir setelah barang dikirim')
                            ->label('Waktu Selesai Muat'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Administrasi')
                    ->schema([
                        Forms\Components\DateTimePicker::make('tanggal_delivery')
                            ->label('Tanggal Pengiriman')
                            ->helperText('Pilih tanggal pengiriman barang'),

                        Forms\Components\Repeater::make('seals')
                            ->label('Nomor Segel')
                            ->relationship('seals')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('nomor_segel')
                                            ->label('Nomor Segel')
                                            ->placeholder('Contoh: SGL-000001')
                                            ->required()
                                            ->maxLength(100),

                                        Forms\Components\Select::make('jenis_segel')
                                            ->label('Jenis Segel')
                                            ->options([
                                                'atas' => 'Segel Atas',
                                                'bawah' => 'Segel Bawah',
                                                'samping' => 'Segel Samping',
                                                'lainnya' => 'Lainnya',
                                            ])
                                            ->default('atas')
                                            ->required(),

                                        Forms\Components\TextInput::make('urutan')
                                            ->label('Urutan')
                                            ->numeric()
                                            ->default(fn($get) => count($get('../../seals')) + 1)
                                            ->minValue(1)
                                            ->maxValue(99),
                                    ]),

                                Forms\Components\Textarea::make('keterangan')
                                    ->label('Keterangan')
                                    ->placeholder('Keterangan tambahan untuk segel ini (opsional)')
                                    ->rows(2)
                                    ->columnSpanFull(),
                            ])
                            ->addActionLabel('Tambah Segel')
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(fn(array $state): ?string => $state['nomor_segel'] ?? 'Segel Baru')
                            ->helperText('Tambahkan satu atau lebih nomor segel untuk delivery order ini')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('do_signatory_name')
                            ->placeholder('Nama Penandatangan DO')
                            ->helperText('Nama penandatangan DO akan otomatis terisi oleh supir setelah barang dikirim')
                            ->label('Nama Penandatangan DO')
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\Toggle::make('do_print_status')
                            ->helperText('Status cetak DO akan otomatis terisi setelah DO dicetak')
                            ->label('Status Cetak DO')
                            ->disabled()
                            ->dehydrated(),



                        Forms\Components\Toggle::make('allowance_receipt_status')
                            ->label('Status Penerimaan Uang Jalan'),
                    ])
                    ->columns(2),
                // Forms\Components\Section::make('Informasi Uang Jalan')
                //     ->schema([
                //         Forms\Components\Fieldset::make('Existing Allowance')
                //             ->schema([
                //                 Forms\Components\TextInput::make('uangJalan.nominal')
                //                     ->label('Allowance Amount')
                //                     ->numeric()
                //                     ->prefix('IDR')
                //                     ->minValue(0),

                //                 Forms\Components\Toggle::make('uangJalan.status_kirim')
                //                     ->label('Sending Status')
                //                     ->helperText('Toggle on if allowance has been sent to driver'),

                //                 Forms\Components\DatePicker::make('uangJalan.tanggal_kirim')
                //                     ->label('Sending Date'),

                //                 Forms\Components\Toggle::make('uangJalan.status_terima')
                //                     ->label('Receipt Status')
                //                     ->helperText('Toggle on if allowance receipt has been confirmed'),

                //                 Forms\Components\DatePicker::make('uangJalan.tanggal_terima')
                //                     ->label('Receipt Date'),

                //                 Forms\Components\FileUpload::make('uangJalan.bukti_kirim')
                //                     ->label('Sending Proof')
                //                     ->directory('allowance-proofs')
                //                     ->visibility('private')
                //                     ->downloadable(),

                //                 Forms\Components\FileUpload::make('uangJalan.bukti_terima')
                //                     ->label('Receipt Proof')
                //                     ->directory('allowance-proofs')
                //                     ->visibility('private')
                //                     ->downloadable(),
                //             ])
                //             ->columns(2)
                //             ->visible(fn($record) => $record && $record->uangJalan),

                //         Forms\Components\Placeholder::make('create_allowance_placeholder')
                //             ->label('No Driver Allowance Record')
                //             ->content('Save this delivery order first, then you can create a driver allowance record.')
                //             ->visible(fn($record) => $record && !$record->uangJalan),
                //     ])
                //     ->collapsible(),

                // Forms\Components\Section::make('Driver Delivery Information')
                //     ->schema([
                //         Forms\Components\Fieldset::make('Delivery Progress')
                //             ->schema([
                //                 Forms\Components\DateTimePicker::make('pengirimanDriver.waktu_mulai')
                //                     ->label('Departure Time'),

                //                 Forms\Components\DateTimePicker::make('pengirimanDriver.waktu_tiba')
                //                     ->label('Arrival Time'),

                //                 Forms\Components\DateTimePicker::make('pengirimanDriver.waktu_pool_arrival')
                //                     ->label('Pool Arrival Time'),


                //             ])
                //             ->columns(2)
                //             ->visible(fn($record) => $record && $record->pengirimanDriver),

                //         Forms\Components\Placeholder::make('create_delivery_placeholder')
                //             ->label('No Driver Delivery Record')
                //             ->content('Save this delivery order first, then you can create a driver delivery record.')
                //             ->visible(fn($record) => $record && !$record->pengirimanDriver),
                //     ])
                //     ->collapsible(),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode')
                    ->label('Nomor DO')

                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('transaksi.nomor_transaksi')
                    ->label('Nomor Transaksi')
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('transaksi', function ($q) use ($search) {
                            $q->where('id', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaksi.kode')
                    ->label('Nomor SO')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('transaksi.pelanggan.nama')
                    ->label('Pelanggan')
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Sopir')
                    ->searchable()
                    ->placeholder('Belum Ditugaskan'),

                Tables\Columns\TextColumn::make('kendaraan.no_pol_kendaraan')
                    ->label('Kendaraan')
                    ->searchable()
                    ->placeholder('Belum Ditugaskan'),

                Tables\Columns\TextColumn::make('volume_do')
                    ->label('Total Volume')
                    ->numeric()
                    ->suffix(' L')
                    ->placeholder('Belum Diisi')
                    ->sortable()
                    ->description(function ($record) {
                        $itemCount = $record->details()->count();
                        return $itemCount > 0 ? "{$itemCount} item" : 'Belum ada detail';
                    }),

                Tables\Columns\TextColumn::make('details_summary')
                    ->label('Detail Items')
                    ->html()
                    ->getStateUsing(function ($record) {
                        $details = $record->details()->with('item')->get();
                        if ($details->isEmpty()) {
                            return '<span class="text-gray-500">Belum ada detail item</span>';
                        }

                        $summary = $details->map(function ($detail) {
                            return $detail->item->name . ': ' . number_format($detail->volume_delivered, 0, ',', '.') . 'L';
                        })->take(2)->implode('<br>');

                        if ($details->count() > 2) {
                            $summary .= '<br><span class="text-gray-500">+' . ($details->count() - 2) . ' item lainnya</span>';
                        }

                        return $summary;
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'in_progress' => 'info',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('sisa_volume_do')
                    ->label('Sisa Volume')
                    ->numeric()
                    ->suffix(' L')
                    ->placeholder('Belum Dihitung')
                    ->sortable(),

                Tables\Columns\TextColumn::make('seal_numbers')
                    ->label('Nomor Segel')
                    ->getStateUsing(fn($record) => $record->seals->pluck('nomor_segel')->implode(', '))
                    ->placeholder('Belum Ada Segel')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('status_muat')
                    ->label('Status Muat')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'muat' => 'info',
                        'selesai' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Perintah Muat Diterbitkan',
                        'muat' => 'Muat Dikonfirmasi',
                        'selesai' => 'Muat Selesai',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('tanggal_delivery')
                    ->label('Tanggal Pengiriman')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\IconColumn::make('do_print_status')
                    ->label('DO Dicetak')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('payment_status')
                    ->label('Status Pembayaran')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'partial' => 'info',
                        'paid' => 'success',
                        'overdue' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Tertunda',
                        'partial' => 'Sebagian',
                        'paid' => 'Lunas',
                        'overdue' => 'Terlambat',
                        default => ucfirst($state),
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_muat')
                    ->label('Loading Status')
                    ->options([
                        'pending' => 'Load Order Issued',
                        'muat' => 'Load Confirmed',
                        'selesai' => 'Loading Complete',
                    ]),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->label('Payment Status')
                    ->options([
                        'pending' => 'Pending',
                        'partial' => 'Partial',
                        'paid' => 'Paid',
                        'overdue' => 'Overdue',
                    ]),

                Tables\Filters\TernaryFilter::make('do_print_status')
                    ->label('DO Print Status')
                    ->placeholder('All')
                    ->trueLabel('Printed')
                    ->falseLabel('Not Printed'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                //     Tables\Actions\Action::make('preview')
                //         ->label('Preview PDF')
                //         ->color('info')
                //         ->icon('heroicon-o-eye')
                //         ->modalHeading(fn(DeliveryOrder $record) => "Preview PDF: {$record->kode}")
                //         ->modalWidth('4xl')
                //         ->slideOver()
                //         ->modalSubmitAction(false)
                //         ->modalCancelActionLabel('Tutup')
                //         ->modalContent(function (DeliveryOrder $record) {
                //             // Load record with all necessary relationships
                //             $deliveryOrder = DeliveryOrder::with([
                //                 'transaksi.pelanggan',
                //                 'transaksi.alamatPelanggan',
                //                 'transaksi.penjualanDetails.item.satuan',
                //                 'transaksi.tbbm',
                //                 'kendaraan',
                //                 'user',
                //                 'uangJalan',
                //                 'pengirimanDriver',
                //                 'seals'
                //             ])->find($record->id);

                //             // Get logo as base64
                //             $logoPath = public_path('images/lrp.png');
                //             $logoBase64 = '';

                //             if (File::exists($logoPath)) {
                //                 $logoBase64 = base64_encode(File::get($logoPath));
                //             }

                //             return view('delivery_order.do-preview', [
                //                 'record' => $deliveryOrder,
                //                 'logoBase64' => $logoBase64
                //             ]);
                //         })
                //         ->extraModalFooterActions([
                //             Tables\Actions\Action::make('download_from_preview')
                //                 ->label('Download PDF')
                //                 ->color('success')
                //                 ->icon('heroicon-o-arrow-down-tray')
                //                 ->action(function (DeliveryOrder $record) {
                //                     // Load record with all necessary relationships
                //                     $deliveryOrder = DeliveryOrder::with([
                //                         'transaksi.pelanggan',
                //                         'transaksi.alamatPelanggan',
                //                         'transaksi.penjualanDetails.item.satuan',
                //                         'transaksi.tbbm',
                //                         'kendaraan',
                //                         'user',
                //                         'uangJalan',
                //                         'pengirimanDriver',
                //                         'seals'
                //                     ])->find($record->id);

                //                     // Get logo as base64
                //                     $logoPath = public_path('images/lrp.png');
                //                     $logoBase64 = '';

                //                     if (File::exists($logoPath)) {
                //                         $logoBase64 = base64_encode(File::get($logoPath));
                //                     }

                //                     // Generate dynamic filename
                //                     $filename = 'DO_' . $deliveryOrder->kode . '_' . now()->format('Ymd_His') . '.pdf';

                //                     // Load the PDF view with the record data
                //                     $pdf = Pdf::loadView('pdf.delivery_order', [
                //                         'record' => $deliveryOrder,
                //                         'logoBase64' => $logoBase64
                //                     ])
                //                         ->setPaper('a4', 'portrait')
                //                         ->setOptions([
                //                             'isHtml5ParserEnabled' => true,
                //                             'isPhpEnabled' => true,
                //                             'defaultFont' => 'Arial'
                //                         ]);

                //                     return response()->streamDownload(function () use ($pdf) {
                //                         echo $pdf->stream();
                //                     }, $filename);
                //                 })
                //         ]),
                //     Tables\Actions\Action::make('pdf')
                //         ->label('Download PDF')
                //         ->color('success')
                //         ->icon('heroicon-o-document-arrow-down')
                //         ->action(function (DeliveryOrder $record) {
                //             // Load record with all necessary relationships
                //             $deliveryOrder = DeliveryOrder::with([
                //                 'transaksi.pelanggan.alamatPelanggan',
                //                 'transaksi.penjualanDetails.item.satuan',
                //                 'kendaraan',
                //                 'user',
                //                 'uangJalan',
                //                 'pengirimanDriver',
                //                 'seals'
                //             ])->find($record->id);

                //             // Get logo as base64
                //             $logoPath = public_path('images/lrp.png');
                //             $logoBase64 = '';

                //             if (File::exists($logoPath)) {
                //                 $logoBase64 = base64_encode(File::get($logoPath));
                //             }

                //             // Generate dynamic filename
                //             $filename = 'DO_' . $deliveryOrder->kode . '_' . now()->format('Ymd_His') . '.pdf';

                //             // Load the PDF view with the record data
                //             $pdf = Pdf::loadView('pdf.delivery_order', [
                //                 'record' => $deliveryOrder,
                //                 'logoBase64' => $logoBase64
                //             ])
                //                 ->setPaper('a4', 'portrait')
                //                 ->setOptions([
                //                     'isHtml5ParserEnabled' => true,
                //                     'isPhpEnabled' => true,
                //                     'defaultFont' => 'Arial'
                //                 ]);

                //             return response()->streamDownload(function () use ($pdf) {
                //                 echo $pdf->stream();
                //             }, $filename);
                //         }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('tanggal_delivery', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeliveryOrders::route('/'),
            'create' => Pages\CreateDeliveryOrder::route('/create'),
            'view' => Pages\ViewDeliveryOrder::route('/{record}'),
            'edit' => Pages\EditDeliveryOrder::route('/{record}/edit'),
        ];
    }
}
